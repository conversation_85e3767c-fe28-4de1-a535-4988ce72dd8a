#### Init
```
<PERSON><PERSON><PERSON> tạo cho tôi 1 kiến trúc project automation test bằng robot framework.
Trong đó có cấu trúc thư mục tôi yêu cầu như sau:
- 1 thư mục lưu trữ các keyword dùng chung
- 1 thư mục testcases cho các luồng test backend
- 1 thư mục testcases cho các luồng test frontend

Và hãy implement cho tôi logic code cơ bản của:
1. Testcase cho api từ curl sau
curl --location --request POST 'http://dev-user-robot.vetc.com.vn/auth-sso/account/login?username=merchanta&password=123456' \ 
--header 'Content-Type: application/x-www-form-urlencoded

Verify kết quả trả về có cấu trúc 
{"message":"Thành công","code":"00","data":{"expiresIn":300,"refreshTokenExpiresIn":1800,"token":"eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBczdTZnd4YzVlUF9OVTdwbHBpejJtOTBVUTE2UXBTZjBpakhFeEpUN2VZIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OOvH-CckoCRK0Qdv1mqTKx7WM4-MyqIArnhKiUOy-rdtmfnm0nDogTWjWoa1KzpczFsAQERkXMJJTEDbPLcyva6-cPu2zmWa9olbZFY2AKLuD0dazx05JoyewayZWWzk8BfoMG5ErJu9mZxvtJVIuffvVBSKZGNsQm1Su8Tg_QCvTSdLI3qkQzW8Iw8fSddD0yhr6KRrcly-LpKU_chkFQVLLncVI099TGVpqxZ2R_M0NkfoKWBKd4YvTum_6Nc1l1ji_hUpCWHfiiTmyxVag16mqVvJN3cEbeQrwW7jvzLvzUxVsHinw0nQuHkvwcOj6nF9qIMrn5wk6BCjri-Zyg","refreshToken":"eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI0MTk5MzE3ZC05OGQyLTQ0N2YtOWNmZS00MDRlNDc3OWIyOGMifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TdEYLg_NAyeFodc4lgNVfyNvvDrxDf5jRPgoN4KF7xrBLxFLBqbwVhiJ6NVeTveDoqRuDMh0xUC-tAJqA268NA","granted":[]},"success":true}

2. User login trang https://dev-admin-robot.vetc.com.vn với mật khẩu => fail
```